#!/usr/bin/env python3
"""
Script de teste para validar as melhorias implementadas no scraper de leads.
Este script executa testes específicos para verificar se as melhorias estão funcionando.
"""

import sys
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By

# Importar as funções melhoradas
import logic_bot

# Configurar logging para o teste
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - TESTE - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('teste_melhorias.log')
    ]
)

logger = logging.getLogger('TesteMelhorias')

def configurar_driver():
    """Configura e retorna um driver do Chrome para testes"""
    try:
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        logger.info("Driver configurado com sucesso")
        return driver
    except Exception as e:
        logger.error(f"Erro ao configurar driver: {str(e)}")
        return None

def teste_seletores_multiplos(driver):
    """Testa se os seletores múltiplos conseguem encontrar elementos"""
    logger.info("=== TESTE: Seletores Múltiplos ===")
    
    try:
        # Navegar para o Google Maps
        driver.get("https://www.google.com.br/maps")
        time.sleep(3)
        
        # Fazer uma busca simples
        logic_bot.buscar_cep(driver, "centro, São Paulo (SP)")
        logic_bot.buscar_palavra_chave(driver, "tecnologia")
        
        # Testar cada seletor
        seletores_elementos = [
            '//a[@class="hfpxzc"]',
            '//a[contains(@class, "hfpxzc")]',
            '//div[@role="article"]//a',
            '//div[contains(@class, "Nv2PK")]//a',
            '//div[@data-result-index]//a',
        ]
        
        resultados_seletores = {}
        
        for i, seletor in enumerate(seletores_elementos, 1):
            try:
                elementos = driver.find_elements(By.XPATH, seletor)
                count = len(elementos)
                resultados_seletores[seletor] = count
                logger.info(f"Seletor {i}: {seletor} -> {count} elementos")
            except Exception as e:
                resultados_seletores[seletor] = 0
                logger.warning(f"Seletor {i} falhou: {str(e)}")
        
        # Verificar se pelo menos um seletor funcionou
        total_elementos = max(resultados_seletores.values())
        if total_elementos > 0:
            logger.info(f"✅ SUCESSO: Encontrados {total_elementos} elementos")
            return True
        else:
            logger.error("❌ FALHA: Nenhum seletor encontrou elementos")
            return False
            
    except Exception as e:
        logger.error(f"❌ ERRO no teste de seletores: {str(e)}")
        return False

def teste_rolagem_melhorada(driver):
    """Testa se a função de rolagem melhorada está funcionando"""
    logger.info("=== TESTE: Rolagem Melhorada ===")
    
    try:
        # Contar elementos antes da rolagem
        elementos_antes = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
        count_antes = len(elementos_antes)
        logger.info(f"Elementos antes da rolagem: {count_antes}")
        
        # Executar rolagem melhorada
        logic_bot.scroll_down(driver)
        
        # Contar elementos depois da rolagem
        time.sleep(2)
        elementos_depois = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
        count_depois = len(elementos_depois)
        logger.info(f"Elementos depois da rolagem: {count_depois}")
        
        if count_depois > count_antes:
            logger.info(f"✅ SUCESSO: Rolagem carregou {count_depois - count_antes} novos elementos")
            return True
        elif count_depois == count_antes:
            logger.warning("⚠️ AVISO: Rolagem não carregou novos elementos (pode ser normal se já chegou ao fim)")
            return True
        else:
            logger.error("❌ FALHA: Perdeu elementos após rolagem")
            return False
            
    except Exception as e:
        logger.error(f"❌ ERRO no teste de rolagem: {str(e)}")
        return False

def teste_estrategias_alternativas(driver):
    """Testa se as estratégias alternativas funcionam"""
    logger.info("=== TESTE: Estratégias Alternativas ===")
    
    try:
        # Contar elementos antes
        elementos_antes = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
        count_antes = len(elementos_antes)
        logger.info(f"Elementos antes das estratégias alternativas: {count_antes}")
        
        # Executar estratégias alternativas
        sucesso = logic_bot.tentar_carregar_mais_resultados(driver)
        
        # Contar elementos depois
        time.sleep(3)
        elementos_depois = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
        count_depois = len(elementos_depois)
        logger.info(f"Elementos depois das estratégias alternativas: {count_depois}")
        
        if sucesso and count_depois > count_antes:
            logger.info(f"✅ SUCESSO: Estratégias alternativas carregaram {count_depois - count_antes} novos elementos")
            return True
        elif not sucesso:
            logger.info("ℹ️ INFO: Estratégias alternativas não encontraram mais resultados (pode ser normal)")
            return True
        else:
            logger.warning("⚠️ AVISO: Estratégias reportaram sucesso mas não carregaram elementos visíveis")
            return True
            
    except Exception as e:
        logger.error(f"❌ ERRO no teste de estratégias alternativas: {str(e)}")
        return False

def teste_extracao_completa(driver):
    """Testa uma extração completa com as melhorias"""
    logger.info("=== TESTE: Extração Completa ===")
    
    try:
        def callback_progresso(atual, total, cliente):
            logger.info(f"Progresso: {atual}/{total} - {cliente['nome']}")
        
        # Executar extração de 5 leads para teste
        clientes = logic_bot.extrair_clientes(driver, 5, callback_progresso)
        
        if clientes and len(clientes) > 0:
            logger.info(f"✅ SUCESSO: Extraídos {len(clientes)} leads")
            for i, cliente in enumerate(clientes, 1):
                logger.info(f"Lead {i}: {cliente['nome']} - {cliente['telefone']}")
            return True
        else:
            logger.error("❌ FALHA: Nenhum lead foi extraído")
            return False
            
    except Exception as e:
        logger.error(f"❌ ERRO no teste de extração completa: {str(e)}")
        return False

def executar_todos_os_testes():
    """Executa todos os testes de validação"""
    logger.info("🚀 INICIANDO TESTES DE VALIDAÇÃO DAS MELHORIAS")
    logger.info("=" * 60)
    
    driver = configurar_driver()
    if not driver:
        logger.error("❌ Falha ao configurar driver. Abortando testes.")
        return False
    
    try:
        resultados = {}
        
        # Executar testes
        resultados['seletores'] = teste_seletores_multiplos(driver)
        resultados['rolagem'] = teste_rolagem_melhorada(driver)
        resultados['estrategias'] = teste_estrategias_alternativas(driver)
        resultados['extracao'] = teste_extracao_completa(driver)
        
        # Resumo dos resultados
        logger.info("=" * 60)
        logger.info("📊 RESUMO DOS TESTES:")
        
        sucessos = 0
        total = len(resultados)
        
        for teste, sucesso in resultados.items():
            status = "✅ PASSOU" if sucesso else "❌ FALHOU"
            logger.info(f"{teste.upper()}: {status}")
            if sucesso:
                sucessos += 1
        
        logger.info(f"\n🎯 RESULTADO FINAL: {sucessos}/{total} testes passaram")
        
        if sucessos == total:
            logger.info("🎉 TODOS OS TESTES PASSARAM! As melhorias estão funcionando corretamente.")
            return True
        elif sucessos >= total * 0.75:
            logger.info("⚠️ A maioria dos testes passou. Algumas melhorias podem precisar de ajustes.")
            return True
        else:
            logger.error("❌ Muitos testes falharam. As melhorias precisam ser revisadas.")
            return False
            
    except Exception as e:
        logger.error(f"❌ ERRO GERAL nos testes: {str(e)}")
        return False
    finally:
        try:
            driver.quit()
            logger.info("Driver fechado com sucesso")
        except:
            pass

if __name__ == "__main__":
    sucesso = executar_todos_os_testes()
    sys.exit(0 if sucesso else 1)
