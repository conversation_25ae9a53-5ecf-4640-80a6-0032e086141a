#!/usr/bin/env python3
"""
Script específico para testar a busca por "PSICÓLOGO" no centro de São Paulo.
Reproduz exatamente o problema relatado no log.
"""

import sys
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Importar as funções melhoradas
import logic_bot

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - TESTE_PSICÓLOGO - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('teste_psicologo.log')
    ]
)

logger = logging.getLogger('TestePsicologo')

def configurar_driver():
    """Configura driver exatamente como no scraper original"""
    try:
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        logger.info("✅ Driver configurado")
        return driver
    except Exception as e:
        logger.error(f"❌ Erro ao configurar driver: {str(e)}")
        return None

def testar_busca_original():
    """Testa a busca exatamente como estava sendo feita antes das melhorias"""
    logger.info("🔍 TESTANDO BUSCA ORIGINAL (antes das melhorias)")
    
    driver = configurar_driver()
    if not driver:
        return False
    
    try:
        # Navegar para Google Maps
        driver.get("https://www.google.com.br/maps")
        time.sleep(3)
        
        # Buscar localização (método original)
        logger.info("Buscando localização: CENTRO, São Paulo (SP)")
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )
        search_box.clear()
        search_box.send_keys("CENTRO, São Paulo (SP)")
        search_box.send_keys(Keys.ENTER)
        time.sleep(5)
        
        # Buscar palavra-chave (método original)
        logger.info("Buscando palavra-chave: PSICÓLOGO")
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )
        search_box.send_keys(Keys.CONTROL + "a")
        search_box.send_keys(Keys.DELETE)
        search_box.send_keys("PSICÓLOGO")
        search_box.send_keys(Keys.ENTER)
        time.sleep(5)
        
        # Verificar resultados (método original)
        try:
            resultados = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.XPATH, '//a[@class="hfpxzc"]'))
            )
            logger.info(f"✅ MÉTODO ORIGINAL: {len(resultados)} resultados encontrados")
            return True
        except:
            logger.warning("❌ MÉTODO ORIGINAL: Nenhum resultado encontrado")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste original: {str(e)}")
        return False
    finally:
        driver.quit()

def testar_busca_melhorada():
    """Testa a busca com as melhorias implementadas"""
    logger.info("🚀 TESTANDO BUSCA MELHORADA (com as melhorias)")
    
    driver = configurar_driver()
    if not driver:
        return False
    
    try:
        # Navegar para Google Maps
        driver.get("https://www.google.com.br/maps")
        time.sleep(3)
        
        # Usar as funções melhoradas
        sucesso_localizacao = logic_bot.buscar_cep(driver, "CENTRO, São Paulo (SP)")
        if not sucesso_localizacao:
            logger.error("❌ Falha ao buscar localização")
            return False
        
        sucesso_palavra_chave = logic_bot.buscar_palavra_chave(driver, "PSICÓLOGO")
        if not sucesso_palavra_chave:
            logger.warning("⚠️ Busca por palavra-chave não retornou resultados")
            return False
        
        logger.info("✅ MÉTODO MELHORADO: Busca bem-sucedida")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erro no teste melhorado: {str(e)}")
        return False
    finally:
        driver.quit()

def testar_termos_alternativos():
    """Testa termos alternativos para psicólogo"""
    logger.info("🔄 TESTANDO TERMOS ALTERNATIVOS")
    
    termos_teste = [
        "psicólogo",  # minúsculo
        "psicologo",  # sem acento
        "psicóloga",  # feminino
        "psicologia",  # área
        "clínica psicológica",
        "consultório psicológico",
        "terapia",
        "psicoterapia",
        "saúde mental"
    ]
    
    driver = configurar_driver()
    if not driver:
        return False
    
    try:
        # Navegar e buscar localização
        driver.get("https://www.google.com.br/maps")
        time.sleep(3)
        
        logic_bot.buscar_cep(driver, "centro, São Paulo (SP)")
        
        resultados_por_termo = {}
        
        for termo in termos_teste:
            logger.info(f"Testando termo: {termo}")
            
            try:
                # Buscar o termo
                search_box = driver.find_element(By.XPATH, '//*[@id="searchboxinput"]')
                search_box.clear()
                search_box.send_keys(termo)
                search_box.send_keys(Keys.ENTER)
                time.sleep(5)
                
                # Contar resultados
                elementos = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
                count = len(elementos)
                resultados_por_termo[termo] = count
                
                if count > 0:
                    logger.info(f"✅ '{termo}': {count} resultados")
                else:
                    logger.info(f"❌ '{termo}': 0 resultados")
                    
            except Exception as e:
                logger.warning(f"Erro ao testar '{termo}': {str(e)}")
                resultados_por_termo[termo] = 0
        
        # Resumo dos resultados
        logger.info("📊 RESUMO DOS TERMOS ALTERNATIVOS:")
        melhores_termos = sorted(resultados_por_termo.items(), key=lambda x: x[1], reverse=True)
        
        for termo, count in melhores_termos:
            if count > 0:
                logger.info(f"✅ {termo}: {count} resultados")
        
        if melhores_termos[0][1] > 0:
            logger.info(f"🏆 MELHOR TERMO: '{melhores_termos[0][0]}' com {melhores_termos[0][1]} resultados")
            return True
        else:
            logger.warning("❌ Nenhum termo alternativo encontrou resultados")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro no teste de termos alternativos: {str(e)}")
        return False
    finally:
        driver.quit()

def verificar_google_maps_manual():
    """Abre o Google Maps para verificação manual"""
    logger.info("🌐 ABRINDO GOOGLE MAPS PARA VERIFICAÇÃO MANUAL")
    
    driver = configurar_driver()
    if not driver:
        return False
    
    try:
        # Navegar para a busca específica
        url_busca = "https://www.google.com.br/maps/search/psic%C3%B3logo+centro+s%C3%A3o+paulo"
        driver.get(url_busca)
        
        logger.info(f"🔗 URL aberta: {url_busca}")
        logger.info("👀 Verifique manualmente se há resultados na página")
        logger.info("⏰ Aguardando 30 segundos para inspeção manual...")
        
        time.sleep(30)
        
        # Verificar se há elementos após carregamento manual
        elementos = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
        if elementos:
            logger.info(f"✅ VERIFICAÇÃO MANUAL: {len(elementos)} elementos encontrados após carregamento")
            return True
        else:
            logger.warning("❌ VERIFICAÇÃO MANUAL: Nenhum elemento encontrado")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erro na verificação manual: {str(e)}")
        return False
    finally:
        input("Pressione Enter para fechar o navegador...")
        driver.quit()

def main():
    """Executa todos os testes para diagnosticar o problema"""
    logger.info("🚀 INICIANDO DIAGNÓSTICO COMPLETO: PSICÓLOGO NO CENTRO DE SP")
    logger.info("=" * 80)
    
    resultados = {}
    
    # Teste 1: Método original
    logger.info("\n1️⃣ TESTE DO MÉTODO ORIGINAL")
    resultados['original'] = testar_busca_original()
    
    # Teste 2: Método melhorado
    logger.info("\n2️⃣ TESTE DO MÉTODO MELHORADO")
    resultados['melhorado'] = testar_busca_melhorada()
    
    # Teste 3: Termos alternativos
    logger.info("\n3️⃣ TESTE DE TERMOS ALTERNATIVOS")
    resultados['alternativos'] = testar_termos_alternativos()
    
    # Teste 4: Verificação manual (opcional)
    if '--manual' in sys.argv:
        logger.info("\n4️⃣ VERIFICAÇÃO MANUAL")
        resultados['manual'] = verificar_google_maps_manual()
    
    # Resumo final
    logger.info("\n" + "=" * 80)
    logger.info("📊 RESUMO FINAL DO DIAGNÓSTICO:")
    
    for teste, sucesso in resultados.items():
        status = "✅ PASSOU" if sucesso else "❌ FALHOU"
        logger.info(f"{teste.upper()}: {status}")
    
    # Conclusões
    if any(resultados.values()):
        logger.info("\n💡 CONCLUSÃO: Pelo menos um método encontrou resultados")
        logger.info("🔧 RECOMENDAÇÃO: Usar o método que funcionou ou termos alternativos")
    else:
        logger.info("\n⚠️ CONCLUSÃO: Nenhum método encontrou resultados")
        logger.info("🔧 POSSÍVEIS CAUSAS:")
        logger.info("   - Realmente não há psicólogos cadastrados no centro de SP no Google Maps")
        logger.info("   - Mudanças recentes na estrutura do Google Maps")
        logger.info("   - Problemas de conectividade ou bloqueios")
        logger.info("   - Necessidade de usar termos mais específicos")

if __name__ == "__main__":
    main()
