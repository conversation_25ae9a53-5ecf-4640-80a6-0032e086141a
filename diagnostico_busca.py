#!/usr/bin/env python3
"""
Script de diagnóstico para problemas de busca no Google Maps.
Ajuda a identificar por que certas buscas não retornam resultados.
"""

import sys
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - DIAGNÓSTICO - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('diagnostico_busca.log')
    ]
)

logger = logging.getLogger('DiagnosticoBusca')

def configurar_driver():
    """Configura e retorna um driver do Chrome"""
    try:
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        logger.info("✅ Driver configurado com sucesso")
        return driver
    except Exception as e:
        logger.error(f"❌ Erro ao configurar driver: {str(e)}")
        return None

def diagnosticar_busca(driver, localizacao, palavra_chave):
    """Executa diagnóstico completo de uma busca"""
    logger.info(f"🔍 INICIANDO DIAGNÓSTICO: '{palavra_chave}' em '{localizacao}'")
    logger.info("=" * 80)
    
    try:
        # 1. Navegar para Google Maps
        logger.info("1️⃣ Navegando para Google Maps...")
        driver.get("https://www.google.com.br/maps")
        time.sleep(3)
        
        # 2. Buscar localização
        logger.info(f"2️⃣ Buscando localização: {localizacao}")
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )
        search_box.clear()
        search_box.send_keys(localizacao)
        search_box.send_keys(Keys.ENTER)
        time.sleep(5)
        
        # Verificar se localização foi encontrada
        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, '//div[@role="main"]'))
            )
            logger.info("✅ Localização encontrada com sucesso")
        except:
            logger.warning("⚠️ Não foi possível confirmar se a localização foi encontrada")
        
        # 3. Buscar palavra-chave
        logger.info(f"3️⃣ Buscando palavra-chave: {palavra_chave}")
        search_box = driver.find_element(By.XPATH, '//*[@id="searchboxinput"]')
        search_box.click()
        time.sleep(0.5)
        search_box.send_keys(Keys.CONTROL + "a")
        search_box.send_keys(Keys.DELETE)
        search_box.send_keys(palavra_chave)
        search_box.send_keys(Keys.ENTER)
        time.sleep(8)
        
        # 4. Analisar resultados
        logger.info("4️⃣ Analisando resultados...")
        
        # Verificar URL atual
        current_url = driver.current_url
        logger.info(f"🔗 URL atual: {current_url}")
        
        # Verificar título da página
        page_title = driver.title
        logger.info(f"📄 Título da página: {page_title}")
        
        # Testar múltiplos seletores
        seletores_teste = [
            ('//a[@class="hfpxzc"]', 'Seletor principal de resultados'),
            ('//a[contains(@class, "hfpxzc")]', 'Seletor flexível de resultados'),
            ('//div[@role="article"]//a', 'Seletor baseado em role'),
            ('//div[contains(@class, "Nv2PK")]//a', 'Seletor de container'),
            ('//div[@data-result-index]//a', 'Seletor de data attribute'),
            ('//div[contains(@class, "THOPZb")]', 'Container alternativo 1'),
            ('//div[contains(@class, "lI9IFe")]', 'Container alternativo 2'),
        ]
        
        resultados_encontrados = False
        for xpath, descricao in seletores_teste:
            try:
                elementos = driver.find_elements(By.XPATH, xpath)
                count = len(elementos)
                if count > 0:
                    logger.info(f"✅ {descricao}: {count} elementos encontrados")
                    resultados_encontrados = True
                    
                    # Mostrar alguns exemplos
                    for i, elem in enumerate(elementos[:3], 1):
                        try:
                            texto = elem.text.strip()[:50]
                            logger.info(f"   Exemplo {i}: {texto}...")
                        except:
                            logger.info(f"   Exemplo {i}: [Erro ao obter texto]")
                else:
                    logger.info(f"❌ {descricao}: 0 elementos")
            except Exception as e:
                logger.info(f"❌ {descricao}: Erro - {str(e)}")
        
        # 5. Verificar mensagens de erro
        logger.info("5️⃣ Verificando mensagens de erro...")
        mensagens_erro = [
            ("//div[contains(text(), 'Nenhum resultado')]", "Nenhum resultado"),
            ("//div[contains(text(), 'No results')]", "No results (inglês)"),
            ("//div[contains(text(), 'Não encontramos')]", "Não encontramos"),
            ("//span[contains(text(), 'Tente uma pesquisa diferente')]", "Sugestão de pesquisa diferente"),
            ("//div[contains(@class, 'section-no-result')]", "Seção sem resultados"),
        ]
        
        for xpath, descricao in mensagens_erro:
            try:
                elemento = driver.find_element(By.XPATH, xpath)
                if elemento.is_displayed():
                    logger.info(f"⚠️ Encontrada mensagem: {descricao}")
                    logger.info(f"   Texto: {elemento.text}")
            except:
                continue
        
        # 6. Capturar screenshot para análise
        try:
            screenshot_path = f"diagnostico_{palavra_chave.replace(' ', '_')}_{int(time.time())}.png"
            driver.save_screenshot(screenshot_path)
            logger.info(f"📸 Screenshot salvo: {screenshot_path}")
        except Exception as e:
            logger.warning(f"Erro ao salvar screenshot: {str(e)}")
        
        # 7. Testar termos alternativos
        if not resultados_encontrados:
            logger.info("6️⃣ Testando termos alternativos...")
            termos_alternativos = [
                f"{palavra_chave} próximo",
                f"{palavra_chave} região",
                f"clínica {palavra_chave}",
                f"consultório {palavra_chave}",
                palavra_chave.lower(),
                palavra_chave.upper(),
            ]
            
            for termo_alt in termos_alternativos:
                logger.info(f"🔄 Testando: {termo_alt}")
                try:
                    search_box = driver.find_element(By.XPATH, '//*[@id="searchboxinput"]')
                    search_box.clear()
                    search_box.send_keys(termo_alt)
                    search_box.send_keys(Keys.ENTER)
                    time.sleep(5)
                    
                    # Verificar se encontrou resultados
                    elementos = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
                    if elementos:
                        logger.info(f"✅ SUCESSO com '{termo_alt}': {len(elementos)} resultados!")
                        break
                    else:
                        logger.info(f"❌ Sem resultados para '{termo_alt}'")
                        
                except Exception as e:
                    logger.warning(f"Erro ao testar '{termo_alt}': {str(e)}")
        
        # 8. Resumo do diagnóstico
        logger.info("=" * 80)
        logger.info("📊 RESUMO DO DIAGNÓSTICO:")
        
        if resultados_encontrados:
            logger.info("✅ STATUS: Resultados encontrados com sucesso")
            logger.info("💡 RECOMENDAÇÃO: O scraper deve funcionar normalmente")
        else:
            logger.info("❌ STATUS: Nenhum resultado encontrado")
            logger.info("💡 POSSÍVEIS CAUSAS:")
            logger.info("   - Termo de busca muito específico ou inexistente na região")
            logger.info("   - Mudanças na estrutura do Google Maps")
            logger.info("   - Problemas de conectividade ou carregamento")
            logger.info("   - Região selecionada não possui estabelecimentos do tipo buscado")
            
            logger.info("🔧 RECOMENDAÇÕES:")
            logger.info("   1. Tentar termos de busca mais genéricos")
            logger.info("   2. Verificar se a região está correta")
            logger.info("   3. Testar manualmente no Google Maps")
            logger.info("   4. Considerar usar termos em português mais comuns")
        
        return resultados_encontrados
        
    except Exception as e:
        logger.error(f"❌ ERRO CRÍTICO no diagnóstico: {str(e)}")
        return False

def main():
    """Função principal do diagnóstico"""
    if len(sys.argv) != 3:
        print("Uso: python diagnostico_busca.py <localização> <palavra-chave>")
        print("Exemplo: python diagnostico_busca.py 'centro, São Paulo (SP)' 'psicólogo'")
        sys.exit(1)
    
    localizacao = sys.argv[1]
    palavra_chave = sys.argv[2]
    
    logger.info("🚀 INICIANDO DIAGNÓSTICO DE BUSCA DO GOOGLE MAPS")
    logger.info(f"📍 Localização: {localizacao}")
    logger.info(f"🔍 Palavra-chave: {palavra_chave}")
    
    driver = configurar_driver()
    if not driver:
        logger.error("❌ Falha ao configurar driver. Abortando diagnóstico.")
        sys.exit(1)
    
    try:
        sucesso = diagnosticar_busca(driver, localizacao, palavra_chave)
        
        if sucesso:
            logger.info("🎉 DIAGNÓSTICO CONCLUÍDO: Busca funcionando corretamente")
            sys.exit(0)
        else:
            logger.info("⚠️ DIAGNÓSTICO CONCLUÍDO: Problemas identificados na busca")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ ERRO GERAL: {str(e)}")
        sys.exit(1)
    finally:
        try:
            driver.quit()
            logger.info("🔒 Driver fechado com sucesso")
        except:
            pass

if __name__ == "__main__":
    main()
